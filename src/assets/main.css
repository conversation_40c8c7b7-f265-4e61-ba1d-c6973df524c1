@import "tailwindcss";

@font-face {
    font-family: 'Cocogoose Classic';
    src: url('fonts/CocogooseClassic-Regular.woff2') format('woff2'),
    url('fonts/CocogooseClassic-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cocogoose Classic';
    src: url('fonts/CocogooseClassic-Light.woff2') format('woff2'),
    url('fonts/CocogooseClassic-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cocogoose Classic';
    src: url('fonts/CocogooseClassic-Bold.woff2') format('woff2'),
    url('fonts/CocogooseClassic-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}


html, body {
    font-family: "Open Sans", sans-serif;
}

h1, h2, h3, h4, h5, h6 {
    font-family: "Cocogoose Classic", sans-serif;
}

.coco {
    font-family: "Cocogoose Classic", sans-serif;
}

.card {
    @apply relative flex flex-col gap-8 break-inside-avoid mb-8 pb-8;
}

.card:nth-child(4) {
    @apply pt-56;
}

.card img {
    @apply w-full h-auto object-cover rounded-2xl;
}

.card ul {
    @apply list-none space-y-3 bg-slate-50 rounded-2xl p-4 border border-slate-100;
}

.card ul li {
    @apply relative pl-7 text-slate-700 leading-relaxed font-medium text-sm;
}

.card ul li::before {
    content: "✓";
    @apply absolute left-0 top-0.5 w-5 h-5 text-xs font-bold flex items-center justify-center;
    font-size: 11px;
    line-height: 1;
}

.masonry-container {
    column-count: 1;
    column-gap: 2rem;
    column-fill: balance;
}

@media (min-width: 768px) {
    .masonry-container {
        column-count: 2;
    }
}